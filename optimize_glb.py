#!/usr/bin/env python3
"""
GLB Optimization Script using meshoptimizer
This script provides a Python interface to optimize GLB files
"""

import subprocess
import os
import sys
import json
import argparse
from pathlib import Path

class GLBOptimizer:
    def __init__(self, meshoptimizer_path="."):
        self.meshoptimizer_path = Path(meshoptimizer_path)
        self.gltfpack_path = self.meshoptimizer_path / "gltfpack"
        
        # Check if gltfpack exists
        if not self.gltfpack_path.exists():
            self.gltfpack_path = self.meshoptimizer_path / "gltfpack.exe"
        
        if not self.gltfpack_path.exists():
            print("Warning: gltfpack not found. You may need to build it first.")

    def optimize_with_gltfpack(self, input_path, output_path, options=None):
        """Optimize using gltfpack with custom options"""
        if not self.gltfpack_path.exists():
            raise FileNotFoundError("gltfpack executable not found")
        
        default_options = {
            'simplification_ratio': 0.5,
            'compression': True,
            'aggressive_compression': False,
            'texture_compression': False,
            'quantization': True,
            'mesh_instancing': False,
            'keep_named_nodes': False,
            'keep_materials': False,
            'vertex_position_float': False
        }
        
        if options:
            default_options.update(options)
        
        cmd = [str(self.gltfpack_path), '-i', str(input_path), '-o', str(output_path)]
        
        # Add optimization flags
        if default_options['simplification_ratio'] < 1.0:
            cmd.extend(['-si', str(default_options['simplification_ratio'])])
        
        if default_options['compression']:
            cmd.append('-c')
        
        if default_options['aggressive_compression']:
            cmd.append('-cc')
        
        if default_options['texture_compression']:
            cmd.append('-tc')
        
        if not default_options['quantization']:
            cmd.append('-noq')
        
        if default_options['mesh_instancing']:
            cmd.append('-mi')
        
        if default_options['keep_named_nodes']:
            cmd.append('-kn')
        
        if default_options['keep_materials']:
            cmd.append('-km')
        
        if default_options['vertex_position_float']:
            cmd.append('-vpf')
        
        print(f"Running: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("Optimization successful!")
            if result.stdout:
                print("Output:", result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Optimization failed: {e}")
            if e.stderr:
                print("Error:", e.stderr)
            return False

    def build_gltfpack(self):
        """Build gltfpack if not available"""
        print("Building gltfpack...")
        
        # Try CMake build
        build_dir = self.meshoptimizer_path / "build"
        build_dir.mkdir(exist_ok=True)
        
        cmake_cmd = [
            'cmake', '..', 
            '-DMESHOPT_BUILD_GLTFPACK=ON',
            '-DCMAKE_BUILD_TYPE=Release'
        ]
        
        try:
            subprocess.run(cmake_cmd, cwd=build_dir, check=True)
            subprocess.run(['cmake', '--build', '.', '--target', 'gltfpack', '--config', 'Release'], 
                         cwd=build_dir, check=True)
            
            # Copy executable to main directory
            if os.name == 'nt':  # Windows
                built_exe = build_dir / "Release" / "gltfpack.exe"
                if not built_exe.exists():
                    built_exe = build_dir / "gltfpack.exe"
            else:  # Unix-like
                built_exe = build_dir / "gltfpack"
            
            if built_exe.exists():
                import shutil
                shutil.copy2(built_exe, self.meshoptimizer_path)
                print("gltfpack built successfully!")
                return True
            else:
                print("Build completed but executable not found")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"Build failed: {e}")
            return False

    def get_file_info(self, file_path):
        """Get information about a GLB file"""
        file_path = Path(file_path)
        if not file_path.exists():
            return None
        
        size_mb = file_path.stat().st_size / (1024 * 1024)
        
        info = {
            'path': str(file_path),
            'size_mb': round(size_mb, 2),
            'exists': True
        }
        
        return info

    def compare_files(self, original_path, optimized_path):
        """Compare original and optimized files"""
        original_info = self.get_file_info(original_path)
        optimized_info = self.get_file_info(optimized_path)
        
        if not original_info or not optimized_info:
            print("Could not compare files - one or both don't exist")
            return
        
        size_reduction = ((original_info['size_mb'] - optimized_info['size_mb']) / 
                         original_info['size_mb'] * 100)
        
        print("\n" + "="*50)
        print("OPTIMIZATION RESULTS")
        print("="*50)
        print(f"Original file:  {original_info['size_mb']:.2f} MB")
        print(f"Optimized file: {optimized_info['size_mb']:.2f} MB")
        print(f"Size reduction: {size_reduction:.1f}%")
        print(f"Space saved:    {original_info['size_mb'] - optimized_info['size_mb']:.2f} MB")
        print("="*50)

    def optimize_with_presets(self, input_path, output_base, presets=None):
        """Create multiple optimized versions with different presets"""
        if presets is None:
            presets = {
                'light': {
                    'simplification_ratio': 0.8,
                    'compression': True,
                    'aggressive_compression': False,
                    'texture_compression': False
                },
                'medium': {
                    'simplification_ratio': 0.5,
                    'compression': True,
                    'aggressive_compression': True,
                    'texture_compression': False
                },
                'aggressive': {
                    'simplification_ratio': 0.3,
                    'compression': True,
                    'aggressive_compression': True,
                    'texture_compression': True
                },
                'ultra': {
                    'simplification_ratio': 0.1,
                    'compression': True,
                    'aggressive_compression': True,
                    'texture_compression': True,
                    'vertex_position_float': True
                }
            }
        
        results = {}
        input_path = Path(input_path)
        
        for preset_name, options in presets.items():
            output_path = f"{output_base}_{preset_name}.glb"
            print(f"\nCreating {preset_name} optimization...")
            
            success = self.optimize_with_gltfpack(input_path, output_path, options)
            if success:
                results[preset_name] = output_path
                self.compare_files(input_path, output_path)
        
        return results

def main():
    parser = argparse.ArgumentParser(description='Optimize GLB files using meshoptimizer')
    parser.add_argument('input', help='Input GLB file path')
    parser.add_argument('-o', '--output', help='Output GLB file path')
    parser.add_argument('-r', '--ratio', type=float, default=0.5, 
                       help='Simplification ratio (0.0-1.0, default: 0.5)')
    parser.add_argument('-c', '--compress', action='store_true', 
                       help='Enable compression')
    parser.add_argument('-cc', '--aggressive-compress', action='store_true',
                       help='Enable aggressive compression')
    parser.add_argument('-tc', '--texture-compress', action='store_true',
                       help='Enable texture compression')
    parser.add_argument('--presets', action='store_true',
                       help='Create multiple versions with different optimization presets')
    parser.add_argument('--build', action='store_true',
                       help='Build gltfpack if not available')
    
    args = parser.parse_args()
    
    optimizer = GLBOptimizer()
    
    if args.build:
        if not optimizer.build_gltfpack():
            sys.exit(1)
        return
    
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"Error: Input file '{input_path}' not found")
        sys.exit(1)
    
    if args.presets:
        output_base = args.output or input_path.stem + "_optimized"
        results = optimizer.optimize_with_presets(input_path, output_base)
        print(f"\nCreated {len(results)} optimized versions:")
        for preset, path in results.items():
            print(f"  {preset}: {path}")
    else:
        output_path = args.output or f"{input_path.stem}_optimized.glb"
        
        options = {
            'simplification_ratio': args.ratio,
            'compression': args.compress,
            'aggressive_compression': args.aggressive_compress,
            'texture_compression': args.texture_compress
        }
        
        success = optimizer.optimize_with_gltfpack(input_path, output_path, options)
        if success:
            optimizer.compare_files(input_path, output_path)
        else:
            sys.exit(1)

if __name__ == '__main__':
    main()
