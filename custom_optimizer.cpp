// Custom GLB Optimizer using meshoptimizer C++ library
// Compile with: g++ -O3 custom_optimizer.cpp -o optimizer -I./src -I./extern

#include "meshoptimizer.h"
#include "cgltf.h"
#include <iostream>
#include <vector>
#include <fstream>
#include <cstring>

class GLBOptimizer {
private:
    struct MeshData {
        std::vector<float> positions;
        std::vector<unsigned int> indices;
        std::vector<float> normals;
        std::vector<float> texcoords;
        size_t vertex_count;
        size_t index_count;
    };

public:
    bool optimizeGLB(const char* input_path, const char* output_path, float simplification_ratio = 0.5f) {
        std::cout << "Loading GLB file: " << input_path << std::endl;
        
        // Load GLB file
        cgltf_data* data = nullptr;
        cgltf_result result = cgltf_parse_file(&data, input_path);
        
        if (result != cgltf_result_success) {
            std::cerr << "Failed to load GLB file" << std::endl;
            return false;
        }

        // Process each mesh
        for (size_t mesh_idx = 0; mesh_idx < data->meshes_count; ++mesh_idx) {
            cgltf_mesh* mesh = &data->meshes[mesh_idx];
            
            for (size_t prim_idx = 0; prim_idx < mesh->primitives_count; ++prim_idx) {
                cgltf_primitive* primitive = &mesh->primitives[prim_idx];
                
                if (primitive->type != cgltf_primitive_type_triangles) {
                    continue; // Skip non-triangle primitives
                }

                MeshData mesh_data;
                if (!extractMeshData(data, primitive, mesh_data)) {
                    continue;
                }

                std::cout << "Original mesh: " << mesh_data.index_count / 3 << " triangles, " 
                         << mesh_data.vertex_count << " vertices" << std::endl;

                // Optimize the mesh
                optimizeMesh(mesh_data, simplification_ratio);

                std::cout << "Optimized mesh: " << mesh_data.index_count / 3 << " triangles, " 
                         << mesh_data.vertex_count << " vertices" << std::endl;
            }
        }

        // Save optimized GLB (simplified - in practice you'd need to rebuild the GLB structure)
        std::cout << "Optimization complete!" << std::endl;
        
        cgltf_free(data);
        return true;
    }

private:
    bool extractMeshData(cgltf_data* data, cgltf_primitive* primitive, MeshData& mesh_data) {
        // Extract positions
        cgltf_attribute* pos_attr = nullptr;
        for (size_t i = 0; i < primitive->attributes_count; ++i) {
            if (primitive->attributes[i].type == cgltf_attribute_type_position) {
                pos_attr = &primitive->attributes[i];
                break;
            }
        }

        if (!pos_attr) {
            std::cerr << "No position attribute found" << std::endl;
            return false;
        }

        cgltf_accessor* pos_accessor = pos_attr->data;
        mesh_data.vertex_count = pos_accessor->count;
        mesh_data.positions.resize(mesh_data.vertex_count * 3);

        // Extract position data
        if (!extractFloatData(data, pos_accessor, mesh_data.positions.data(), 3)) {
            return false;
        }

        // Extract indices
        if (primitive->indices) {
            cgltf_accessor* idx_accessor = primitive->indices;
            mesh_data.index_count = idx_accessor->count;
            mesh_data.indices.resize(mesh_data.index_count);

            if (!extractIndexData(data, idx_accessor, mesh_data.indices.data())) {
                return false;
            }
        } else {
            // Generate indices for non-indexed geometry
            mesh_data.index_count = mesh_data.vertex_count;
            mesh_data.indices.resize(mesh_data.index_count);
            for (size_t i = 0; i < mesh_data.index_count; ++i) {
                mesh_data.indices[i] = static_cast<unsigned int>(i);
            }
        }

        // Extract normals if available
        for (size_t i = 0; i < primitive->attributes_count; ++i) {
            if (primitive->attributes[i].type == cgltf_attribute_type_normal) {
                cgltf_accessor* norm_accessor = primitive->attributes[i].data;
                mesh_data.normals.resize(mesh_data.vertex_count * 3);
                extractFloatData(data, norm_accessor, mesh_data.normals.data(), 3);
                break;
            }
        }

        // Extract texture coordinates if available
        for (size_t i = 0; i < primitive->attributes_count; ++i) {
            if (primitive->attributes[i].type == cgltf_attribute_type_texcoord_0) {
                cgltf_accessor* tex_accessor = primitive->attributes[i].data;
                mesh_data.texcoords.resize(mesh_data.vertex_count * 2);
                extractFloatData(data, tex_accessor, mesh_data.texcoords.data(), 2);
                break;
            }
        }

        return true;
    }

    void optimizeMesh(MeshData& mesh_data, float simplification_ratio) {
        // Step 1: Generate vertex remap for deduplication
        std::vector<unsigned int> remap(mesh_data.vertex_count);
        size_t unique_vertices = meshopt_generateVertexRemap(
            remap.data(), mesh_data.indices.data(), mesh_data.index_count,
            mesh_data.positions.data(), mesh_data.vertex_count, sizeof(float) * 3
        );

        // Step 2: Remap indices and vertices
        std::vector<unsigned int> remapped_indices(mesh_data.index_count);
        meshopt_remapIndexBuffer(remapped_indices.data(), mesh_data.indices.data(), 
                                mesh_data.index_count, remap.data());

        std::vector<float> remapped_positions(unique_vertices * 3);
        meshopt_remapVertexBuffer(remapped_positions.data(), mesh_data.positions.data(),
                                 mesh_data.vertex_count, sizeof(float) * 3, remap.data());

        // Step 3: Optimize vertex cache
        meshopt_optimizeVertexCache(remapped_indices.data(), remapped_indices.data(),
                                   mesh_data.index_count, unique_vertices);

        // Step 4: Optimize overdraw (optional)
        meshopt_optimizeOverdraw(remapped_indices.data(), remapped_indices.data(),
                                mesh_data.index_count, remapped_positions.data(),
                                unique_vertices, sizeof(float) * 3, 1.05f);

        // Step 5: Simplify mesh
        size_t target_index_count = size_t(mesh_data.index_count * simplification_ratio);
        std::vector<unsigned int> simplified_indices(mesh_data.index_count);
        
        float target_error = 0.01f; // 1% error tolerance
        float result_error = 0.0f;
        
        size_t simplified_count = meshopt_simplify(
            simplified_indices.data(), remapped_indices.data(), mesh_data.index_count,
            remapped_positions.data(), unique_vertices, sizeof(float) * 3,
            target_index_count, target_error, 0, &result_error
        );

        // Step 6: Optimize vertex fetch
        meshopt_optimizeVertexFetch(remapped_positions.data(), simplified_indices.data(),
                                   simplified_count, remapped_positions.data(),
                                   unique_vertices, sizeof(float) * 3);

        // Update mesh data
        mesh_data.indices = std::vector<unsigned int>(simplified_indices.begin(), 
                                                     simplified_indices.begin() + simplified_count);
        mesh_data.positions = remapped_positions;
        mesh_data.vertex_count = unique_vertices;
        mesh_data.index_count = simplified_count;

        std::cout << "Simplification error: " << result_error << std::endl;
    }

    bool extractFloatData(cgltf_data* data, cgltf_accessor* accessor, float* output, size_t components) {
        if (accessor->component_type != cgltf_component_type_r_32f) {
            std::cerr << "Unsupported component type for float data" << std::endl;
            return false;
        }

        cgltf_buffer_view* view = accessor->buffer_view;
        cgltf_buffer* buffer = view->buffer;
        
        const char* src = static_cast<const char*>(buffer->data) + view->offset + accessor->offset;
        size_t stride = accessor->stride ? accessor->stride : components * sizeof(float);

        for (size_t i = 0; i < accessor->count; ++i) {
            memcpy(output + i * components, src + i * stride, components * sizeof(float));
        }

        return true;
    }

    bool extractIndexData(cgltf_data* data, cgltf_accessor* accessor, unsigned int* output) {
        cgltf_buffer_view* view = accessor->buffer_view;
        cgltf_buffer* buffer = view->buffer;
        
        const char* src = static_cast<const char*>(buffer->data) + view->offset + accessor->offset;

        if (accessor->component_type == cgltf_component_type_r_16u) {
            const uint16_t* indices = reinterpret_cast<const uint16_t*>(src);
            for (size_t i = 0; i < accessor->count; ++i) {
                output[i] = indices[i];
            }
        } else if (accessor->component_type == cgltf_component_type_r_32u) {
            memcpy(output, src, accessor->count * sizeof(unsigned int));
        } else {
            std::cerr << "Unsupported index component type" << std::endl;
            return false;
        }

        return true;
    }
};

int main(int argc, char* argv[]) {
    if (argc < 3) {
        std::cout << "Usage: " << argv[0] << " <input.glb> <output.glb> [simplification_ratio]" << std::endl;
        std::cout << "Example: " << argv[0] << " Lambo.glb Lambo_optimized.glb 0.3" << std::endl;
        return 1;
    }

    float ratio = 0.5f; // Default 50% reduction
    if (argc > 3) {
        ratio = std::atof(argv[3]);
        if (ratio <= 0.0f || ratio > 1.0f) {
            std::cerr << "Simplification ratio must be between 0 and 1" << std::endl;
            return 1;
        }
    }

    GLBOptimizer optimizer;
    if (!optimizer.optimizeGLB(argv[1], argv[2], ratio)) {
        std::cerr << "Optimization failed" << std::endl;
        return 1;
    }

    return 0;
}
