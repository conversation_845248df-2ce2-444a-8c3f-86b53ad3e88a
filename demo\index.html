<!doctype html>
<html lang="en">
	<head>
		<title>meshoptimizer - demo</title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
		<style>
			body {
				font-family: Monospace;
				background-color: #000;
				color: #fff;
				margin: 0px;
				overflow: hidden;
			}
			#info {
				color: #fff;
				position: absolute;
				top: 10px;
				width: 100%;
				text-align: center;
				z-index: 100;
				display: block;
			}
			#info a,
			.button {
				color: #f00;
				font-weight: bold;
				text-decoration: underline;
				cursor: pointer;
			}
		</style>

		<script async src="https://cdn.jsdelivr.net/npm/es-module-shims@2.0.10/dist/es-module-shims.min.js"></script>
		<script type="importmap">
			{
				"imports": {
					"three": "https://cdn.jsdelivr.net/npm/three@0.174.0/build/three.module.js",
					"three-examples/": "https://cdn.jsdelivr.net/npm/three@0.174.0/examples/jsm/"
				}
			}
		</script>
	</head>

	<body>
		<div id="info">
			<a href="https://github.com/zeux/meshoptimizer" target="_blank" rel="noopener">meshoptimizer</a>
		</div>

		<script type="module">
			import * as THREE from 'three';
			import { GLTFLoader } from 'three-examples/loaders/GLTFLoader.js';
			import { MeshoptDecoder } from '../js/meshopt_decoder.module.js';

			var container;

			var camera, scene, renderer, mixer, clock;

			var windowHalfX = window.innerWidth / 2;
			var windowHalfY = window.innerHeight / 2;

			init();
			animate();

			function init() {
				container = document.createElement('div');
				document.body.appendChild(container);

				camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.01, 100);
				camera.position.y = 1.0;
				camera.position.z = 3.0;

				scene = new THREE.Scene();
				scene.background = new THREE.Color(0x300a24);

				var ambientLight = new THREE.AmbientLight(0xcccccc, 1);
				scene.add(ambientLight);

				var pointLight = new THREE.PointLight(0xffffff, 4);
				pointLight.position.set(3, 3, 0);
				pointLight.decay = 0.5;
				camera.add(pointLight);
				scene.add(camera);

				var onProgress = function (xhr) {};
				var onError = function (e) {
					console.log(e);
				};

				var loader = new GLTFLoader();
				loader.setMeshoptDecoder(MeshoptDecoder);
				loader.load(
					'pirate.glb',
					function (gltf) {
						var bbox = new THREE.Box3().setFromObject(gltf.scene);
						var scale = 2 / (bbox.max.y - bbox.min.y);

						gltf.scene.scale.set(scale, scale, scale);
						gltf.scene.position.set(0, 0, 0);

						scene.add(gltf.scene);

						mixer = new THREE.AnimationMixer(gltf.scene);

						if (gltf.animations.length) {
							mixer.clipAction(gltf.animations[gltf.animations.length - 1]).play();
						}
					},
					onProgress,
					onError
				);

				renderer = new THREE.WebGLRenderer();
				renderer.setPixelRatio(window.devicePixelRatio);
				renderer.setSize(window.innerWidth, window.innerHeight);
				container.appendChild(renderer.domElement);

				window.addEventListener('resize', onWindowResize, false);

				clock = new THREE.Clock();
			}

			function onWindowResize() {
				windowHalfX = window.innerWidth / 2;
				windowHalfY = window.innerHeight / 2;

				camera.aspect = window.innerWidth / window.innerHeight;
				camera.updateProjectionMatrix();

				renderer.setSize(window.innerWidth, window.innerHeight);
			}

			function animate() {
				if (mixer) {
					mixer.update(clock.getDelta());
				}

				requestAnimationFrame(animate);
				render();
			}

			function render() {
				renderer.render(scene, camera);
			}
		</script>
	</body>
</html>
