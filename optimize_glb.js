// GLB Optimization Script using meshoptimizer.js
// This script provides programmatic control over mesh optimization

import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter.js';
import { MeshoptDecoder } from './js/meshopt_decoder.module.js';
import { MeshoptSimplifier } from './js/meshopt_simplifier.module.js';
import { MeshoptEncoder } from './js/meshopt_encoder.module.js';

class GLBOptimizer {
    constructor() {
        this.loader = new GLTFLoader();
        this.exporter = new GLTFExporter();
        this.loader.setMeshoptDecoder(MeshoptDecoder);
    }

    async optimizeGLB(inputPath, outputPath, options = {}) {
        const defaultOptions = {
            simplificationRatio: 0.5,        // Reduce to 50% of original triangles
            targetError: 0.01,               // 1% error tolerance
            preserveNormals: true,
            preserveUVs: true,
            preserveColors: true,
            lockBorder: false,
            enablePruning: true,
            compressionLevel: 2,             // 0=none, 1=basic, 2=aggressive
            quantizationBits: 14             // Position quantization
        };

        const config = { ...defaultOptions, ...options };
        
        console.log('Loading GLB file:', inputPath);
        const gltf = await this.loadGLB(inputPath);
        
        console.log('Optimizing meshes...');
        await this.optimizeMeshes(gltf.scene, config);
        
        console.log('Exporting optimized GLB...');
        await this.exportGLB(gltf.scene, outputPath, config);
        
        console.log('Optimization complete!');
    }

    loadGLB(path) {
        return new Promise((resolve, reject) => {
            this.loader.load(path, resolve, undefined, reject);
        });
    }

    async optimizeMeshes(scene, config) {
        await MeshoptSimplifier.ready;
        
        const stats = { originalTriangles: 0, optimizedTriangles: 0 };
        
        scene.traverse((object) => {
            if (object.isMesh && object.geometry.index) {
                const originalTriCount = object.geometry.index.count / 3;
                stats.originalTriangles += originalTriCount;
                
                // Simplify the mesh
                const optimizedGeometry = this.simplifyMesh(object.geometry, config);
                object.geometry = optimizedGeometry;
                
                const newTriCount = optimizedGeometry.index.count / 3;
                stats.optimizedTriangles += newTriCount;
                
                console.log(`Mesh optimized: ${originalTriCount} → ${newTriCount} triangles`);
            }
        });
        
        const reduction = ((stats.originalTriangles - stats.optimizedTriangles) / stats.originalTriangles * 100).toFixed(1);
        console.log(`Total reduction: ${reduction}% (${stats.originalTriangles} → ${stats.optimizedTriangles} triangles)`);
    }

    simplifyMesh(geometry, config) {
        // Prepare data for simplification
        const positions = new Float32Array(geometry.attributes.position.array);
        const indices = new Uint32Array(geometry.index.array);
        
        // Calculate target triangle count
        const targetCount = Math.floor(indices.length * config.simplificationRatio / 3) * 3;
        
        // Prepare attributes if needed
        let attributes = null;
        let attributeWeights = null;
        
        if (config.preserveNormals || config.preserveUVs || config.preserveColors) {
            const attrSize = 8; // 3 normal + 3 color + 2 UV
            attributes = new Float32Array(geometry.attributes.position.count * attrSize);
            attributeWeights = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.5, 0.5]; // weights for each attribute
            
            for (let i = 0; i < geometry.attributes.position.count; i++) {
                let offset = 0;
                
                // Normals
                if (geometry.attributes.normal && config.preserveNormals) {
                    attributes[i * attrSize + offset++] = geometry.attributes.normal.getX(i);
                    attributes[i * attrSize + offset++] = geometry.attributes.normal.getY(i);
                    attributes[i * attrSize + offset++] = geometry.attributes.normal.getZ(i);
                } else {
                    offset += 3;
                }
                
                // Colors
                if (geometry.attributes.color && config.preserveColors) {
                    attributes[i * attrSize + offset++] = geometry.attributes.color.getX(i);
                    attributes[i * attrSize + offset++] = geometry.attributes.color.getY(i);
                    attributes[i * attrSize + offset++] = geometry.attributes.color.getZ(i);
                } else {
                    offset += 3;
                }
                
                // UVs
                if (geometry.attributes.uv && config.preserveUVs) {
                    attributes[i * attrSize + offset++] = geometry.attributes.uv.getX(i);
                    attributes[i * attrSize + offset++] = geometry.attributes.uv.getY(i);
                } else {
                    offset += 2;
                }
            }
        }

        // Set up simplification flags
        const flags = [];
        if (config.lockBorder) flags.push('LockBorder');
        if (config.enablePruning) flags.push('Prune');

        // Perform simplification
        const stride = 3; // position stride
        let result;
        
        if (attributes) {
            result = MeshoptSimplifier.simplifyWithAttributes(
                indices, positions, stride,
                attributes, 8, attributeWeights, null,
                targetCount, config.targetError, flags
            );
        } else {
            result = MeshoptSimplifier.simplify(
                indices, positions, stride,
                targetCount, config.targetError, flags
            );
        }

        // Create new geometry with simplified indices
        const newGeometry = geometry.clone();
        newGeometry.index.array = result[0];
        newGeometry.index.count = result[0].length;
        newGeometry.index.needsUpdate = true;

        return newGeometry;
    }

    exportGLB(scene, outputPath, config) {
        return new Promise((resolve, reject) => {
            const options = {
                binary: true,
                maxTextureSize: 1024,
                includeCustomExtensions: true
            };

            this.exporter.parse(scene, (result) => {
                // In a real implementation, you'd write this to file
                // For now, we'll just log the size
                const size = result.byteLength;
                console.log(`Optimized GLB size: ${(size / 1024 / 1024).toFixed(2)} MB`);
                resolve(result);
            }, reject, options);
        });
    }
}

// Usage example
async function optimizeLamboGLB() {
    const optimizer = new GLBOptimizer();
    
    const options = {
        simplificationRatio: 0.3,    // Reduce to 30% of original triangles
        targetError: 0.005,          // 0.5% error tolerance
        preserveNormals: true,
        preserveUVs: true,
        lockBorder: false,
        enablePruning: true
    };
    
    try {
        await optimizer.optimizeGLB('./Lambo.glb', './Lambo_optimized.glb', options);
    } catch (error) {
        console.error('Optimization failed:', error);
    }
}

// Run the optimization
optimizeLamboGLB();
