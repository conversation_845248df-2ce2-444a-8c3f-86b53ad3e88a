meshoptimizer is widely used in the games industry as well as in many pipelines for processing 3D content for real-time rendering. This document contains a small selection of projects that rely on meshoptimizer.

For brevity, the projects listed below are limited to commercial software or open source software that has 5000+ stars on GitHub. Please feel free to contribute additions via pull requests.

# Games

If you are shipping a game that is using meshoptimizer, listing the library in the credits or documentation would be appreciated!

- [Alan Wake 2 (2023)](https://www.remedygames.com/games/alan-wake-2)
- [Baldur's Gate 3 (2023)](https://baldursgate3.game/)
- [Cities: Skylines II (2023)](https://www.paradoxinteractive.com/games/cities-skylines-ii/about)
- [Counter-Strike 2 (2023)](https://www.counter-strike.net/cs2)
- [Deadlock](https://store.steampowered.com/app/1422450/Deadlock/)
- [Dota 2 (2013)](https://www.dota2.com/home)
- [Dragon's Dogma 2 (2024)](https://www.dragonsdogma.com/2/en-us/)
- [Half-Life: Alyx (2020)](https://store.steampowered.com/app/546560/HalfLife_Alyx/)
- [Jagged Alliance 3 (2023)](https://jaggedalliance3.thqnordic.com/)
- [The Legend of Zelda: Tears of the Kingdom (2023)](https://zelda.nintendo.com/tears-of-the-kingdom/)
- [Manifold Garden (2019)](https://manifold.garden)
- [Monaco 2 (2025)](https://store.steampowered.com/app/1063030/Monaco_2/)
- [Monster Hunter Wilds (2025)](https://www.monsterhunter.com/wilds/en-us/)
- [Ravenswatch (2023)](https://store.steampowered.com/app/2071280/Ravenswatch/)
- [The Settlers: New Allies (2023)](https://www.ubisoft.com/en-us/game/the-settlers/new-allies)
- [Sid Meier's Civilization VII (2025)](https://civilization.2k.com/civ-vii/)
- [Sky: Children of the Light (2019)](https://www.thatskygame.com/)
- [Stranded: Alien Dawn (2023)](https://www.strandedaliendawn.com/en-US)
- [Tunnet (2023)](https://store.steampowered.com/app/2286390/Tunnet/)

# Engines

If you are shipping an engine that is using meshoptimizer, listing the library in the documentation would be appreciated!

- [Bevy](https://bevyengine.org/)
- [bgfx](https://github.com/bkaradzic/bgfx)
- [filament](https://github.com/google/filament)
- [Flax Engine](https://flaxengine.com/)
- [The Forge](https://theforge.dev/)
- [Godot](https://godotengine.org/)
- [HypeHype](https://hypehype.com/en)
- [Luxe Engine](https://luxeengine.com/)
- [LWJGL](https://www.lwjgl.org/)
- [Qt Quick 3D](https://doc.qt.io/qt-6/qtquick3d-index.html)
- [Roblox](https://www.roblox.com/)
- [Unigine](https://unigine.com/)
- [Wicked Engine](https://wickedengine.net/)

meshoptimizer is also used in [Waymo](https://waymo.com/) internal stack.

# Web

meshoptimizer supports various web projects through [meshoptimizer.js](https://www.npmjs.com/package/meshoptimizer).
For projects in the glTF ecosystem, [gltfpack](https://github.com/zeux/meshoptimizer/tree/master/gltf#-gltfpack) is developed alongside meshoptimizer, and `EXT_meshopt_compression` glTF extension is implemented by meshoptimizer.js.

This list contains a small selection of popular projects in the web ecosystem that use meshoptimizer; see also [a list of projects that depend on meshoptimizer through NPM](https://github.com/zeux/meshoptimizer/network/dependents).

- [aframe](https://aframe.io/)
- [Babylon.js](https://www.babylonjs.com/)
- [CesiumJS](https://cesium.com/platform/cesiumjs/)
- [three.js](https://threejs.org/)
- [glTF-Transform](https://gltf-transform.dev/)
- [Wonderland Engine](https://wonderlandengine.com/)
