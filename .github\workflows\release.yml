name: release

on:
  push:
    branches:
      - 'master'
    paths-ignore:
      - '*.md'

jobs:
  gltfpack:
    strategy:
      matrix:
        os: [{ name: windows, version: windows-latest }, { name: ubuntu, version: ubuntu-22.04 }, { name: macos, version: macos-14 }]
    name: gltfpack-${{matrix.os.name}}
    runs-on: ${{matrix.os.version}}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/checkout@v4
        with:
          repository: zeux/basis_universal
          ref: gltfpack
          path: basis_universal
      - name: cmake configure
        run: cmake . -DMESHOPT_BUILD_GLTFPACK=ON -DMESHOPT_BASISU_PATH=basis_universal -DMESHOPT_WERROR=ON -DCMAKE_MSVC_RUNTIME_LIBRARY="MultiThreaded" -DCMAKE_BUILD_TYPE=Release -DCMAKE_OSX_ARCHITECTURES="x86_64;arm64"
      - name: cmake build
        run: cmake --build . --target gltfpack --config Release -j 2
      - uses: actions/upload-artifact@v4
        with:
          name: gltfpack-windows
          path: Release/gltfpack.exe
        if: matrix.os.name == 'windows'
      - uses: actions/upload-artifact@v4
        with:
          name: gltfpack-${{matrix.os.name}}
          path: gltfpack
        if: matrix.os.name != 'windows'

  nodejs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: install wasi
        run: |
          curl -sL https://github.com/WebAssembly/wasi-sdk/releases/download/wasi-sdk-$VERSION/wasi-sdk-$VERSION.0-x86_64-linux.deb > wasi-sdk.deb
          sudo dpkg -i wasi-sdk.deb
        env:
          VERSION: 25
      - name: build
        run: |
          make -j2 -B gltf/library.wasm js
          git status
      - name: npm pack
        run: |
          cp LICENSE.md gltf/
          cp LICENSE.md js/
          cd gltf && npm pack && cd ..
          cd js && npm pack && cd ..
      - uses: actions/upload-artifact@v4
        with:
          name: gltfpack-npm
          path: gltf/gltfpack-*.tgz
      - uses: actions/upload-artifact@v4
        with:
          name: meshoptimizer-npm
          path: js/meshoptimizer-*.tgz
